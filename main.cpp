#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
#include <climits>

using namespace std;

struct Server {
    int npus;      // NPU数量
    int speed;     // 推理速度系数ki
    int memory;    // 显存大小mi
};

struct User {
    int start_time;  // si
    int end_time;    // ei
    int samples;     // cnti
};

struct Request {
    int time;
    int server;
    int npu;
    int batch_size;
};

// 计算推理耗时
int calculateInferenceTime(int batch_size, int speed_coeff) {
    double f_b = speed_coeff * sqrt(batch_size);
    return (int)ceil(batch_size / f_b);
}

// 计算显存需求
int calculateMemory(int batch_size, int a, int b) {
    return a * batch_size + b;
}

// 选择最优服务器和NPU
pair<int, int> selectBestServerNPU(const vector<Server>& servers,
                                   const vector<vector<int>>& latency,
                                   int user_id, int batch_size, int a, int b) {
    int best_server = 0, best_npu = 0;
    double best_score = -1;

    for (int i = 0; i < servers.size(); i++) {
        // 检查显存约束
        if (calculateMemory(batch_size, a, b) > servers[i].memory) {
            continue;
        }

        // 计算推理时间
        int inference_time = calculateInferenceTime(batch_size, servers[i].speed);

        // 计算总时间（通信延迟 + 推理时间）
        int total_time = latency[i][user_id] + inference_time;

        // 综合评分：考虑推理效率和通信延迟
        double throughput = (double)batch_size / total_time;
        double score = throughput;

        if (score > best_score) {
            best_score = score;
            best_server = i;
            best_npu = 0; // 简化：总是选择第一个NPU，减少迁移
        }
    }

    return {best_server, best_npu};
}

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int N;
    cin >> N;

    vector<Server> servers(N);
    for (int i = 0; i < N; i++) {
        cin >> servers[i].npus >> servers[i].speed >> servers[i].memory;
    }

    int M;
    cin >> M;

    vector<User> users(M);
    for (int i = 0; i < M; i++) {
        cin >> users[i].start_time >> users[i].end_time >> users[i].samples;
    }

    vector<vector<int>> latency(N, vector<int>(M));
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < M; j++) {
            cin >> latency[i][j];
        }
    }

    int a, b;
    cin >> a >> b;

    // 为每个用户生成调度方案
    for (int user_id = 0; user_id < M; user_id++) {
        vector<Request> requests;

        int remaining_samples = users[user_id].samples;
        int current_time = users[user_id].start_time;
        int time_window = users[user_id].end_time - users[user_id].start_time;

        // 找到全局最大可用batch_size
        int global_max_batch = 1;
        for (int bs = min(1000, remaining_samples); bs >= 1; bs--) {
            bool valid = false;
            for (int i = 0; i < N; i++) {
                if (calculateMemory(bs, a, b) <= servers[i].memory) {
                    valid = true;
                    break;
                }
            }
            if (valid) {
                global_max_batch = bs;
                break;
            }
        }

        // 确保至少有一个可用的batch_size
        if (global_max_batch == 1) {
            bool has_valid_server = false;
            for (int i = 0; i < N; i++) {
                if (calculateMemory(1, a, b) <= servers[i].memory) {
                    has_valid_server = true;
                    break;
                }
            }
            if (!has_valid_server) {
                // 应急处理：选择显存最大的服务器
                global_max_batch = 1;
            }
        }

        // 智能调度：根据时间窗口和样本数量动态调整策略
        int last_server = -1; // 记录上次使用的服务器，减少迁移

        while (remaining_samples > 0) {
            // 动态计算batch_size
            int remaining_time = users[user_id].end_time - current_time;
            int adaptive_batch = min(global_max_batch, remaining_samples);

            // 如果时间紧张，尽量使用更大的batch_size
            if (remaining_time < time_window / 3 && remaining_samples > adaptive_batch) {
                adaptive_batch = min(global_max_batch, remaining_samples);
            }

            // 确保batch_size至少为1
            adaptive_batch = max(1, adaptive_batch);

            // 选择最优服务器和NPU，优先考虑上次使用的服务器
            auto [server_id, npu_id] = selectBestServerNPU(servers, latency, user_id, adaptive_batch, a, b);

            // 如果上次服务器仍然可用且性能差距不大，优先使用以减少迁移
            if (last_server != -1 && last_server < N) {
                if (calculateMemory(adaptive_batch, a, b) <= servers[last_server].memory) {
                    int current_time_cost = latency[server_id][user_id] +
                                          calculateInferenceTime(adaptive_batch, servers[server_id].speed);
                    int last_time_cost = latency[last_server][user_id] +
                                       calculateInferenceTime(adaptive_batch, servers[last_server].speed);

                    // 如果性能差距不超过20%，继续使用上次的服务器
                    if (last_time_cost <= current_time_cost * 1.2) {
                        server_id = last_server;
                    }
                }
            }

            requests.push_back({current_time, server_id + 1, npu_id + 1, adaptive_batch});
            last_server = server_id;

            remaining_samples -= adaptive_batch;

            // 更新下次发送时间
            if (remaining_samples > 0) {
                current_time += latency[server_id][user_id] + 1;

                // 时间窗口检查
                if (current_time >= users[user_id].end_time) {
                    // 时间不够，将剩余样本合并到最后一个请求
                    if (!requests.empty()) {
                        // 检查显存是否足够
                        int new_batch = requests.back().batch_size + remaining_samples;
                        int server_idx = requests.back().server - 1;
                        if (calculateMemory(new_batch, a, b) <= servers[server_idx].memory) {
                            requests.back().batch_size = new_batch;
                        } else {
                            // 显存不够，创建新请求但时间设为最后可能的时间
                            current_time = users[user_id].end_time - 1;
                            auto [new_server_id, new_npu_id] = selectBestServerNPU(servers, latency, user_id, remaining_samples, a, b);
                            requests.push_back({current_time, new_server_id + 1, new_npu_id + 1, remaining_samples});
                        }
                        remaining_samples = 0;
                    }
                }
            }
        }

        // 输出结果
        cout << requests.size() << "\n";
        for (int i = 0; i < requests.size(); i++) {
            if (i > 0) cout << " ";
            cout << requests[i].time << " " << requests[i].server << " "
                 << requests[i].npu << " " << requests[i].batch_size;
        }
        cout << "\n";
    }

    return 0;
}